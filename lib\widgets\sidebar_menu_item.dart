import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../theme/app_theme.dart';

class SidebarMenuItem extends StatelessWidget {
  final String icon;
  final String title;
  final bool isActive;

  const SidebarMenuItem({
    super.key,
    required this.icon,
    required this.title,
    this.isActive = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      decoration: BoxDecoration(
        gradient: isActive
            ? const LinearGradient(
                colors: [AppTheme.primaryColor, Colors.transparent],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              )
            : null,
        border: isActive
            ? const Border(
                left: BorderSide(color: AppTheme.primaryColor, width: 1),
              )
            : null,
      ),
      child: Stack(
        children: [
          if (isActive)
            Positioned(
              left: -1,
              top: 0,
              bottom: 0,
              width: 3,
              child: Container(
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor,
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.primaryColor,
                      blurRadius: 5,
                    ),
                  ],
                ),
              ),
            ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
            child: Row(
              children: [
                SvgPicture.asset(
                  icon,
                  width: 16,
                  height: 16,
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: isActive
                            ? AppTheme.textColor
                            : AppTheme.textColorSecondary,
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
