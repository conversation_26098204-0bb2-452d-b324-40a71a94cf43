import 'package:flutter/material.dart';
import '../l10n/app_localizations.dart';
import '../theme/app_theme.dart';
import 'dashboard_card.dart';
import 'monthly_chart.dart';

class MainContent extends StatelessWidget {
  const MainContent({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return Container(
      color: AppTheme.backgroundColor,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(30),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              localizations.dashboard,
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 10),
            Text(
              localizations.dashboardDescription,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textColorSecondary,
                  ),
            ),
            const SizedBox(height: 30),
            
            // Cards grid
            Wrap(
              spacing: 20,
              runSpacing: 20,
              children: [
                // Bitcoin card
                DashboardCard(
                  width: 280,
                  height: 240,
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Image.asset('images/badge-background.svg', width: 45, height: 47),
                          const SizedBox(width: 8),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                localizations.bitcoin,
                                style: Theme.of(context).textTheme.bodyLarge,
                              ),
                              Text(
                                localizations.btc,
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 10),
                      Expanded(
                        child: Image.asset('images/chart-image.svg'),
                      ),
                      const SizedBox(height: 10),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '29,850.15',
                            style: Theme.of(context).textTheme.bodyLarge,
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              '+ 20%',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: AppTheme.positiveColor,
                                  ),
                            ),
                          ),
                        ],
                      ),
                      Text(
                        '2.73 BTC',
                        style: Theme.of(context).textTheme.bodySmall,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                
                // Wave card
                DashboardCard(
                  width: 280,
                  height: 240,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: Image.asset(
                          'images/card-thumbnail-1.png',
                          height: 110,
                          width: double.infinity,
                          fit: BoxFit.cover,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        localizations.wave,
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                      Text(
                        'wav2 #5672',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      const Spacer(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Image.asset('images/currency-icon.svg', height: 15, width: 7.5),
                              const SizedBox(width: 6),
                              Text(
                                '0.018',
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: AppTheme.textColor,
                                    ),
                              ),
                            ],
                          ),
                          Text(
                            '5/160',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                // Heart Rate card
                DashboardCard(
                  width: 280,
                  height: 240,
                  child: Column(
                    children: [
                      Expanded(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              padding: const EdgeInsets.all(10),
                              decoration: BoxDecoration(
                                color: AppTheme.cardBackgroundColor,
                                borderRadius: BorderRadius.circular(50),
                              ),
                              child: Image.asset('images/feature-icon.svg', width: 24, height: 24),
                            ),
                            Text(
                              localizations.heartRate,
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                            Text(
                              localizations.lastSevenDays,
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ],
                        ),
                      ),
                      const Divider(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '74 ms',
                                style: Theme.of(context).textTheme.displayMedium,
                              ),
                              Text(
                                localizations.avgThisWeek,
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                            ],
                          ),
                          Container(
                            width: 36,
                            height: 36,
                            decoration: const BoxDecoration(
                              color: AppTheme.accentColor,
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                              child: Image.asset('images/action-button-icon.png', width: 19, height: 21),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                // Master Bedroom card
                DashboardCard(
                  width: 280,
                  height: 240,
                  padding: EdgeInsets.zero,
                  child: Column(
                    children: [
                      Stack(
                        children: [
                          Image.asset(
                            'images/card-thumbnail-2.png',
                            height: 110,
                            width: double.infinity,
                            fit: BoxFit.cover,
                          ),
                          Positioned(
                            top: 10,
                            right: 10,
                            child: Image.asset('images/favorite-icon.png', width: 22, height: 22),
                          ),
                        ],
                      ),
                      Padding(
                        padding: const EdgeInsets.all(12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              localizations.masterBedroom,
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                            Text(
                              '4 ${localizations.devices}',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: AppTheme.textColorSecondary,
                                  ),
                            ),
                            const SizedBox(height: 12),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  localizations.on,
                                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                        color: AppTheme.textColorSecondary,
                                      ),
                                ),
                                Container(
                                  width: 51,
                                  height: 31,
                                  decoration: const BoxDecoration(
                                    color: AppTheme.accentColor,
                                    borderRadius: BorderRadius.all(Radius.circular(20)),
                                  ),
                                  child: Align(
                                    alignment: Alignment.centerRight,
                                    child: Padding(
                                      padding: const EdgeInsets.only(right: 2),
                                      child: Image.asset('images/toggle-button-icon.svg', width: 27, height: 27),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Thermostats card
                DashboardCard(
                  width: 280,
                  height: 240,
                  child: Column(
                    children: [
                      Expanded(
                        child: Column(
                          children: [
                            Container(
                              width: 44,
                              height: 44,
                              decoration: BoxDecoration(
                                color: AppTheme.cardBackgroundColor,
                                borderRadius: BorderRadius.circular(32),
                                border: Border.all(color: AppTheme.borderColor),
                              ),
                              child: Center(
                                child: Image.asset('images/wallet-icon.png', width: 14, height: 21),
                              ),
                            ),
                            const SizedBox(height: 16),
                            Column(
                              children: [
                                Text(
                                  localizations.thermostats,
                                  style: Theme.of(context).textTheme.titleLarge,
                                ),
                                Text(
                                  '1 ${localizations.device}',
                                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                        color: AppTheme.textColorSecondary,
                                      ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const Divider(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            localizations.on,
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          Image.asset('images/switch-background.svg', width: 68, height: 36),
                        ],
                      ),
                    ],
                  ),
                ),
                
                // Spending card
                DashboardCard(
                  width: 280,
                  height: 240,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset('images/circular-icon.svg', width: 112, height: 112),
                      const SizedBox(height: 28),
                      Text(
                        localizations.spending,
                        style: Theme.of(context).textTheme.bodySmall,
                        textAlign: TextAlign.center,
                      ),
                      Text(
                        '3 978.78',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppTheme.textColor,
                            ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                
                // Walk card
                DashboardCard(
                  width: 280,
                  height: 240,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        children: [
                          Image.asset('images/apple-logo.png', width: 11, height: 13),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              localizations.walk,
                              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                                    color: AppTheme.textColorSecondary,
                                  ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 14),
                      Stack(
                        alignment: Alignment.center,
                        children: [
                          Image.asset('images/progress-ring-outer.svg', width: 130, height: 130),
                          Image.asset('images/progress-ring-inner.svg', width: 123, height: 123),
                          Column(
                            children: [
                              Text(
                                '9549',
                                style: Theme.of(context).textTheme.displayMedium,
                              ),
                              Text(
                                localizations.steps,
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                // Age card
                DashboardCard(
                  width: 280,
                  height: 240,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            localizations.age,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: AppTheme.textColorSecondary,
                                ),
                          ),
                          Image.asset('images/arrow-icon.png', width: 21, height: 13),
                        ],
                      ),
                      const SizedBox(height: 20),
                      Text(
                        '28',
                        style: Theme.of(context).textTheme.displayLarge,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 30),
            
            // Monthly analytics section
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      localizations.spending,
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    Text(
                      localizations.monthlyAnalytics,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppTheme.textColorSecondary,
                          ),
                    ),
                  ],
                ),
                Container(
                  decoration: BoxDecoration(
                    color: AppTheme.cardBackgroundColor,
                    borderRadius: BorderRadius.circular(99),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                        child: Text(
                          localizations.daily,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: AppTheme.textColor,
                              ),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                        child: Text(
                          localizations.monthly,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: AppTheme.textColor,
                              ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Monthly chart
            const MonthlyChart(),
            
            const SizedBox(height: 20),
            
            // Export button
            Align(
              alignment: Alignment.centerRight,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                decoration: BoxDecoration(
                  color: AppTheme.cardBackgroundColor,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      localizations.export,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppTheme.textColor,
                          ),
                    ),
                    const SizedBox(width: 8),
                    Image.asset('images/download-icon.svg', width: 16, height: 16),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
