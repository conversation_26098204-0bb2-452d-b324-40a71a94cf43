import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    'dashboard': MessageLookupByLibrary.simpleMessage('Dashboard'),
    'dashboardDescription': MessageLookupByLibrary.simpleMessage('AI-empowered financial optimization. One app for all your fiscal needs.'),
    'foundation': MessageLookupByLibrary.simpleMessage('FOUNDATION'),
    'colors': MessageLookupByLibrary.simpleMessage('Colors'),
    'spacing': MessageLookupByLibrary.simpleMessage('Spacing'),
    'shadowsAndBlurs': MessageLookupByLibrary.simpleMessage('Shadows & Blurs'),
    'components': MessageLookupByLibrary.simpleMessage('COMPONENTS'),
    'content': MessageLookupByLibrary.simpleMessage('Content'),
    'buttons': MessageLookupByLibrary.simpleMessage('Buttons'),
    'menus': MessageLookupByLibrary.simpleMessage('Menus'),
    'cards': MessageLookupByLibrary.simpleMessage('Cards'),
    'wireframes': MessageLookupByLibrary.simpleMessage('Wireframes'),
    'icons': MessageLookupByLibrary.simpleMessage('Icons'),
    'backgrounds': MessageLookupByLibrary.simpleMessage('Backgrounds'),
    'patterns': MessageLookupByLibrary.simpleMessage('Patterns'),
    'updateIcons': MessageLookupByLibrary.simpleMessage('Update icons'),
    'pricingSections': MessageLookupByLibrary.simpleMessage('Pricing sections'),
    'cardComponents': MessageLookupByLibrary.simpleMessage('Card components'),
    'appTemplate': MessageLookupByLibrary.simpleMessage('App template'),
    'bitcoin': MessageLookupByLibrary.simpleMessage('Bitcoin'),
    'btc': MessageLookupByLibrary.simpleMessage('BTC'),
    'wave': MessageLookupByLibrary.simpleMessage('Wave'),
    'heartRate': MessageLookupByLibrary.simpleMessage('Heart Rate'),
    'lastSevenDays': MessageLookupByLibrary.simpleMessage('Last 7 days'),
    'avgThisWeek': MessageLookupByLibrary.simpleMessage('Avg. this week'),
    'masterBedroom': MessageLookupByLibrary.simpleMessage('Master Bedroom'),
    'devices': MessageLookupByLibrary.simpleMessage('devices'),
    'on': MessageLookupByLibrary.simpleMessage('ON'),
    'thermostats': MessageLookupByLibrary.simpleMessage('Thermostats'),
    'device': MessageLookupByLibrary.simpleMessage('device'),
    'spending': MessageLookupByLibrary.simpleMessage('SPENDING'),
    'walk': MessageLookupByLibrary.simpleMessage('Walk'),
    'steps': MessageLookupByLibrary.simpleMessage('Steps'),
    'age': MessageLookupByLibrary.simpleMessage('AGE'),
    'monthlyAnalytics': MessageLookupByLibrary.simpleMessage('Monthly analytics'),
    'daily': MessageLookupByLibrary.simpleMessage('Daily'),
    'monthly': MessageLookupByLibrary.simpleMessage('Monthly'),
    'export': MessageLookupByLibrary.simpleMessage('Export'),
    'balance': MessageLookupByLibrary.simpleMessage('Balance'),
    'creditCard': MessageLookupByLibrary.simpleMessage('CREDIT CARD'),
    'validThru': MessageLookupByLibrary.simpleMessage('Valid thru'),
    'cardDetails': MessageLookupByLibrary.simpleMessage('Card details'),
    'cardNumber': MessageLookupByLibrary.simpleMessage('Card number'),
    'currency': MessageLookupByLibrary.simpleMessage('Currency'),
    'statusCard': MessageLookupByLibrary.simpleMessage('Status card'),
    'active': MessageLookupByLibrary.simpleMessage('Active'),
    'of': MessageLookupByLibrary.simpleMessage('of'),
    'notifications': MessageLookupByLibrary.simpleMessage('Notifications'),
    'seeAll': MessageLookupByLibrary.simpleMessage('See all'),
    'preparedAReport': MessageLookupByLibrary.simpleMessage('prepared a report'),
    'invitedYouToAChat': MessageLookupByLibrary.simpleMessage('invited you to a chat'),
    'invitedYouToAMeeting': MessageLookupByLibrary.simpleMessage('invited you to a meeting'),
    'minutesAgo': MessageLookupByLibrary.simpleMessage('m ago'),
    'daysAgo': MessageLookupByLibrary.simpleMessage('d ago'),
    'unread': MessageLookupByLibrary.simpleMessage('unread'),
    'markAllAsRead': MessageLookupByLibrary.simpleMessage('Mark all as read'),
  };
}
