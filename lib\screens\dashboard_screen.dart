import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../l10n/app_localizations.dart';
import '../providers/locale_provider.dart';
import '../widgets/sidebar.dart';
import '../widgets/main_content.dart';
import '../widgets/right_panel.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRtl = localeProvider.locale.languageCode == 'ar';
    
    return Directionality(
      textDirection: isRtl ? TextDirection.rtl : TextDirection.ltr,
      child: Scaffold(
        body: SafeArea(
          child: Row(
            children: [
              // Sidebar
              const Sidebar(),
              
              // Main content
              const Expanded(
                child: MainContent(),
              ),
              
              // Right panel
              const RightPanel(),
            ],
          ),
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () => localeProvider.toggleLocale(),
          child: Icon(isRtl ? Icons.language : Icons.translate),
          tooltip: isRtl ? 'Switch to English' : 'التبديل إلى العربية',
        ),
      ),
    );
  }
}
