import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'messages_all.dart';

class AppLocalizations {
  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  static Future<AppLocalizations> load(Locale locale) {
    final String name = locale.countryCode == null || locale.countryCode!.isEmpty
        ? locale.languageCode
        : locale.toString();
    final String localeName = Intl.canonicalizedLocale(name);

    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      return AppLocalizations();
    });
  }

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  String get dashboard => Intl.message(
    'Dashboard',
    name: 'dashboard',
  );

  String get dashboardDescription => Intl.message(
    'AI-empowered financial optimization. One app for all your fiscal needs.',
    name: 'dashboardDescription',
  );

  String get foundation => Intl.message(
    'FOUNDATION',
    name: 'foundation',
  );

  String get colors => Intl.message(
    'Colors',
    name: 'colors',
  );

  String get spacing => Intl.message(
    'Spacing',
    name: 'spacing',
  );

  String get shadowsAndBlurs => Intl.message(
    'Shadows & Blurs',
    name: 'shadowsAndBlurs',
  );

  String get components => Intl.message(
    'COMPONENTS',
    name: 'components',
  );

  String get content => Intl.message(
    'Content',
    name: 'content',
  );

  String get buttons => Intl.message(
    'Buttons',
    name: 'buttons',
  );

  String get menus => Intl.message(
    'Menus',
    name: 'menus',
  );

  String get cards => Intl.message(
    'Cards',
    name: 'cards',
  );

  String get wireframes => Intl.message(
    'Wireframes',
    name: 'wireframes',
  );

  String get icons => Intl.message(
    'Icons',
    name: 'icons',
  );

  String get backgrounds => Intl.message(
    'Backgrounds',
    name: 'backgrounds',
  );

  String get patterns => Intl.message(
    'Patterns',
    name: 'patterns',
  );

  String get updateIcons => Intl.message(
    'Update icons',
    name: 'updateIcons',
  );

  String get pricingSections => Intl.message(
    'Pricing sections',
    name: 'pricingSections',
  );

  String get cardComponents => Intl.message(
    'Card components',
    name: 'cardComponents',
  );

  String get appTemplate => Intl.message(
    'App template',
    name: 'appTemplate',
  );

  String get bitcoin => Intl.message(
    'Bitcoin',
    name: 'bitcoin',
  );

  String get btc => Intl.message(
    'BTC',
    name: 'btc',
  );

  String get wave => Intl.message(
    'Wave',
    name: 'wave',
  );

  String get heartRate => Intl.message(
    'Heart Rate',
    name: 'heartRate',
  );

  String get lastSevenDays => Intl.message(
    'Last 7 days',
    name: 'lastSevenDays',
  );

  String get avgThisWeek => Intl.message(
    'Avg. this week',
    name: 'avgThisWeek',
  );

  String get masterBedroom => Intl.message(
    'Master Bedroom',
    name: 'masterBedroom',
  );

  String get devices => Intl.message(
    'devices',
    name: 'devices',
  );

  String get on => Intl.message(
    'ON',
    name: 'on',
  );

  String get thermostats => Intl.message(
    'Thermostats',
    name: 'thermostats',
  );

  String get device => Intl.message(
    'device',
    name: 'device',
  );

  String get spending => Intl.message(
    'SPENDING',
    name: 'spending',
  );

  String get walk => Intl.message(
    'Walk',
    name: 'walk',
  );

  String get steps => Intl.message(
    'Steps',
    name: 'steps',
  );

  String get age => Intl.message(
    'AGE',
    name: 'age',
  );

  String get monthlyAnalytics => Intl.message(
    'Monthly analytics',
    name: 'monthlyAnalytics',
  );

  String get daily => Intl.message(
    'Daily',
    name: 'daily',
  );

  String get monthly => Intl.message(
    'Monthly',
    name: 'monthly',
  );

  String get export => Intl.message(
    'Export',
    name: 'export',
  );

  String get balance => Intl.message(
    'Balance',
    name: 'balance',
  );

  String get creditCard => Intl.message(
    'CREDIT CARD',
    name: 'creditCard',
  );

  String get validThru => Intl.message(
    'Valid thru',
    name: 'validThru',
  );

  String get cardDetails => Intl.message(
    'Card details',
    name: 'cardDetails',
  );

  String get cardNumber => Intl.message(
    'Card number',
    name: 'cardNumber',
  );

  String get currency => Intl.message(
    'Currency',
    name: 'currency',
  );

  String get statusCard => Intl.message(
    'Status card',
    name: 'statusCard',
  );

  String get active => Intl.message(
    'Active',
    name: 'active',
  );

  String get of => Intl.message(
    'of',
    name: 'of',
  );

  String get notifications => Intl.message(
    'Notifications',
    name: 'notifications',
  );

  String get seeAll => Intl.message(
    'See all',
    name: 'seeAll',
  );

  String get preparedAReport => Intl.message(
    'prepared a report',
    name: 'preparedAReport',
  );

  String get invitedYouToAChat => Intl.message(
    'invited you to a chat',
    name: 'invitedYouToAChat',
  );

  String get invitedYouToAMeeting => Intl.message(
    'invited you to a meeting',
    name: 'invitedYouToAMeeting',
  );

  String get minutesAgo => Intl.message(
    'm ago',
    name: 'minutesAgo',
  );

  String get daysAgo => Intl.message(
    'd ago',
    name: 'daysAgo',
  );

  String get unread => Intl.message(
    'unread',
    name: 'unread',
  );

  String get markAllAsRead => Intl.message(
    'Mark all as read',
    name: 'markAllAsRead',
  );
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['en', 'ar'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) {
    return AppLocalizations.load(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
