import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

class <PERSON><PERSON>hart extends StatelessWidget {
  const MonthlyChart({super.key});

  @override
  Widget build(BuildContext context) {
    final months = ['JAN', 'FEB', 'MAR', 'APR', 'MAI', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.cardBackgroundColor,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Y-axis labels
              Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: List.generate(6, (index) {
                  final value = (6 - index) * 10;
                  return Container(
                    height: 33.6,
                    alignment: Alignment.center,
                    child: Text(
                      value.toString(),
                      style: Theme.of(context).textTheme.labelSmall,
                      textAlign: TextAlign.center,
                    ),
                  );
                }),
              ),
              
              const SizedBox(width: 12),
              
              // Chart content
              Expanded(
                child: SizedBox(
                  height: 200,
                  child: Stack(
                    children: [
                      // Horizontal grid lines
                      Positioned.fill(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: List.generate(6, (index) {
                            return Container(
                              height: 0.7,
                              color: AppTheme.borderColor,
                            );
                          }),
                        ),
                      ),
                      
                      // Bar chart
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: List.generate(12, (index) {
                          // Random heights for demonstration
                          final leftHeight = [168.7, 135.8, 123.2, 147.7, 135.8, 151.2, 135.8, 135.8, 135.8, 162.4, 142.1, 162.4][index];
                          final rightHeight = [141.4, 135.8, 37.1, 102.9, 119.0, 119.0, 102.9, 71.4, 42.7, 102.9, 135.8, 155.4][index];
                          
                          return SizedBox(
                            width: 16.8,
                            child: Row(
                              children: [
                                Container(
                                  width: 8.4,
                                  height: leftHeight,
                                  decoration: BoxDecoration(
                                    color: Colors.grey[800],
                                    borderRadius: BorderRadius.circular(4.2),
                                  ),
                                ),
                                Container(
                                  width: 8.4,
                                  height: rightHeight,
                                  decoration: BoxDecoration(
                                    color: AppTheme.accentColor,
                                    borderRadius: BorderRadius.circular(4.2),
                                  ),
                                ),
                              ],
                            ),
                          );
                        }),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8.4),
          
          // X-axis labels
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: months.map((month) {
              return SizedBox(
                width: 22,
                child: Text(
                  month,
                  style: Theme.of(context).textTheme.labelSmall,
                  textAlign: TextAlign.center,
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}
