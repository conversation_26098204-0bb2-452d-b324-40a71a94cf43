import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    'dashboard': MessageLookupByLibrary.simpleMessage('لوحة التحكم'),
    'dashboardDescription': MessageLookupByLibrary.simpleMessage('تحسين مالي مدعوم بالذكاء الاصطناعي. تطبيق واحد لجميع احتياجاتك المالية.'),
    'foundation': MessageLookupByLibrary.simpleMessage('الأساسيات'),
    'colors': MessageLookupByLibrary.simpleMessage('الألوان'),
    'spacing': MessageLookupByLibrary.simpleMessage('المسافات'),
    'shadowsAndBlurs': MessageLookupByLibrary.simpleMessage('الظلال والتمويه'),
    'components': MessageLookupByLibrary.simpleMessage('المكونات'),
    'content': MessageLookupByLibrary.simpleMessage('المحتوى'),
    'buttons': MessageLookupByLibrary.simpleMessage('الأزرار'),
    'menus': MessageLookupByLibrary.simpleMessage('القوائم'),
    'cards': MessageLookupByLibrary.simpleMessage('البطاقات'),
    'wireframes': MessageLookupByLibrary.simpleMessage('الإطارات السلكية'),
    'icons': MessageLookupByLibrary.simpleMessage('الأيقونات'),
    'backgrounds': MessageLookupByLibrary.simpleMessage('الخلفيات'),
    'patterns': MessageLookupByLibrary.simpleMessage('الأنماط'),
    'updateIcons': MessageLookupByLibrary.simpleMessage('تحديث الأيقونات'),
    'pricingSections': MessageLookupByLibrary.simpleMessage('أقسام التسعير'),
    'cardComponents': MessageLookupByLibrary.simpleMessage('مكونات البطاقة'),
    'appTemplate': MessageLookupByLibrary.simpleMessage('قالب التطبيق'),
    'bitcoin': MessageLookupByLibrary.simpleMessage('بيتكوين'),
    'btc': MessageLookupByLibrary.simpleMessage('BTC'),
    'wave': MessageLookupByLibrary.simpleMessage('ويف'),
    'heartRate': MessageLookupByLibrary.simpleMessage('معدل ضربات القلب'),
    'lastSevenDays': MessageLookupByLibrary.simpleMessage('آخر 7 أيام'),
    'avgThisWeek': MessageLookupByLibrary.simpleMessage('متوسط هذا الأسبوع'),
    'masterBedroom': MessageLookupByLibrary.simpleMessage('غرفة النوم الرئيسية'),
    'devices': MessageLookupByLibrary.simpleMessage('أجهزة'),
    'on': MessageLookupByLibrary.simpleMessage('تشغيل'),
    'thermostats': MessageLookupByLibrary.simpleMessage('منظمات الحرارة'),
    'device': MessageLookupByLibrary.simpleMessage('جهاز'),
    'spending': MessageLookupByLibrary.simpleMessage('الإنفاق'),
    'walk': MessageLookupByLibrary.simpleMessage('المشي'),
    'steps': MessageLookupByLibrary.simpleMessage('خطوات'),
    'age': MessageLookupByLibrary.simpleMessage('العمر'),
    'monthlyAnalytics': MessageLookupByLibrary.simpleMessage('تحليلات شهرية'),
    'daily': MessageLookupByLibrary.simpleMessage('يومي'),
    'monthly': MessageLookupByLibrary.simpleMessage('شهري'),
    'export': MessageLookupByLibrary.simpleMessage('تصدير'),
    'balance': MessageLookupByLibrary.simpleMessage('الرصيد'),
    'creditCard': MessageLookupByLibrary.simpleMessage('بطاقة ائتمان'),
    'validThru': MessageLookupByLibrary.simpleMessage('صالحة حتى'),
    'cardDetails': MessageLookupByLibrary.simpleMessage('تفاصيل البطاقة'),
    'cardNumber': MessageLookupByLibrary.simpleMessage('رقم البطاقة'),
    'currency': MessageLookupByLibrary.simpleMessage('العملة'),
    'statusCard': MessageLookupByLibrary.simpleMessage('حالة البطاقة'),
    'active': MessageLookupByLibrary.simpleMessage('نشطة'),
    'of': MessageLookupByLibrary.simpleMessage('من'),
    'notifications': MessageLookupByLibrary.simpleMessage('الإشعارات'),
    'seeAll': MessageLookupByLibrary.simpleMessage('عرض الكل'),
    'preparedAReport': MessageLookupByLibrary.simpleMessage('أعد تقريراً'),
    'invitedYouToAChat': MessageLookupByLibrary.simpleMessage('دعاك إلى محادثة'),
    'invitedYouToAMeeting': MessageLookupByLibrary.simpleMessage('دعاك إلى اجتماع'),
    'minutesAgo': MessageLookupByLibrary.simpleMessage('د مضت'),
    'daysAgo': MessageLookupByLibrary.simpleMessage('ي مضت'),
    'unread': MessageLookupByLibrary.simpleMessage('غير مقروءة'),
    'markAllAsRead': MessageLookupByLibrary.simpleMessage('تحديد الكل كمقروء'),
  };
}
