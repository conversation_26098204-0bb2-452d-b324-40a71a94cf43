import 'package:flutter/material.dart';
import '../l10n/app_localizations.dart';
import '../theme/app_theme.dart';
import 'sidebar_menu_item.dart';
import 'sidebar_profile_item.dart';

class Sidebar extends StatelessWidget {
  const Sidebar({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return Container(
      width: 240,
      decoration: const BoxDecoration(
        color: AppTheme.backgroundColor,
        border: Border(
          right: BorderSide(color: AppTheme.borderColor),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.fromLTRB(16, 40, 16, 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Foundation section
                  Text(
                    localizations.foundation,
                    style: Theme.of(context).textTheme.labelMedium,
                  ),
                  const SizedBox(height: 12),
                  SidebarMenuItem(
                    icon: 'images/sidebar-icon-1.svg',
                    title: localizations.colors,
                    isActive: true,
                  ),
                  SidebarMenuItem(
                    icon: 'images/sidebar-icon-2.svg',
                    title: localizations.spacing,
                  ),
                  SidebarMenuItem(
                    icon: 'images/sidebar-icon-3.svg',
                    title: localizations.shadowsAndBlurs,
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Components section
                  Text(
                    localizations.components,
                    style: Theme.of(context).textTheme.labelMedium,
                  ),
                  const SizedBox(height: 12),
                  SidebarMenuItem(
                    icon: 'images/sidebar-icon-4.svg',
                    title: localizations.content,
                  ),
                  SidebarMenuItem(
                    icon: 'images/sidebar-icon-5.svg',
                    title: localizations.buttons,
                  ),
                  SidebarMenuItem(
                    icon: 'images/sidebar-icon-6.svg',
                    title: localizations.menus,
                  ),
                  SidebarMenuItem(
                    icon: 'images/sidebar-icon-7.svg',
                    title: localizations.cards,
                  ),
                  SidebarMenuItem(
                    icon: 'images/sidebar-icon-8.svg',
                    title: localizations.wireframes,
                  ),
                  SidebarMenuItem(
                    icon: 'images/sidebar-icon-9.svg',
                    title: localizations.icons,
                  ),
                  SidebarMenuItem(
                    icon: 'images/sidebar-icon-10.svg',
                    title: localizations.backgrounds,
                  ),
                  SidebarMenuItem(
                    icon: 'images/sidebar-icon-11.svg',
                    title: localizations.patterns,
                  ),
                ],
              ),
            ),
          ),
          
          // Profile section
          Container(
            padding: const EdgeInsets.all(10),
            child: Column(
              children: [
                SidebarProfileItem(
                  avatar: 'images/profile-avatar-1.svg',
                  name: '1',
                  description: localizations.updateIcons,
                ),
                SidebarProfileItem(
                  avatar: 'images/profile-avatar-2.svg',
                  name: '2',
                  description: localizations.pricingSections,
                ),
                SidebarProfileItem(
                  avatar: 'images/profile-avatar-3.svg',
                  name: '3',
                  description: localizations.cardComponents,
                ),
                SidebarProfileItem(
                  avatar: 'images/profile-avatar-4.svg',
                  name: '4',
                  description: localizations.appTemplate,
                ),
                SidebarProfileItem(
                  avatar: 'images/profile-avatar-5.svg',
                  name: '5',
                  description: localizations.patterns,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
